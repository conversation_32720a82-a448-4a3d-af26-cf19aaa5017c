{"version": 3, "sources": ["../../../../scss/future/users.r.scss", "../../../../scss/future/_users.scss", "users.r.css"], "names": [], "mappings": "AAAA;;;;;;;EAAA;ACAA;EACE,yBAAA;EACA,6BAAA;EACA,yBAAA;ACSF;ADPE;EACE,WAAA;EACA,YAAA;ACSJ", "file": "users.r.rtl.css", "sourcesContent": ["/**\n * NukeViet Content Management System\n * @version 5.x\n * <AUTHOR> <<EMAIL>>\n * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved\n * @license GNU/GPL version 2 or any later version\n * @see https://github.com/nukeviet The NukeViet CMS GitHub project\n */\n\n$enable-responsive: true;\n\n@import \"../../node_modules/bootstrap/scss/functions\";\n@import \"../../node_modules/bootstrap/scss/variables\";\n@import \"../../node_modules/bootstrap/scss/variables-dark\";\n@import \"../../node_modules/bootstrap/scss/maps\";\n@import \"../../node_modules/bootstrap/scss/mixins\";\n@import \"style_header\";\n\n@import \"users\";\n", ".user-bldropdown {\n  width: calc(250px + 2rem);\n  max-width: calc(100vw - 1rem);\n  padding: 1rem 1rem 0 1rem;\n\n  .no-avatar {\n    width: 80px;\n    height: 80px;\n  }\n}\n", "/**\n * NukeViet Content Management System\n * @version 5.x\n * <AUTHOR> <<EMAIL>>\n * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved\n * @license GNU/GPL version 2 or any later version\n * @see https://github.com/nukeviet The NukeViet CMS GitHub project\n */\n.user-bldropdown {\n  width: calc(250px + 2rem);\n  max-width: calc(100vw - 1rem);\n  padding: 1rem 1rem 0 1rem;\n}\n.user-bldropdown .no-avatar {\n  width: 80px;\n  height: 80px;\n}\n\n/*# sourceMappingURL=users.r.css.map */\n"]}