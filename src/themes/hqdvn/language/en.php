<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '20/07/2023, 07:15';
$lang_translator['copyright'] = '@Copyright (C) 2010 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['tconf_default'] = 'Default';
$lang_module['tconf_color_mode'] = 'Color mode';
$lang_module['tconf_cm_light'] = 'Light';
$lang_module['tconf_cm_dark'] = 'Dark';
$lang_module['tconf_cm_auto'] = 'Auto';
$lang_module['tconf_light_theme'] = 'Light theme';
$lang_module['tconf_customize_variables'] = 'Variables';
$lang_module['tconf_css'] = 'CSS';
$lang_module['tconf_gfonts'] = 'Google fonts';
