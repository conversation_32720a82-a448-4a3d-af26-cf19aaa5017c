name: Tests

on: ['push', 'pull_request']

jobs:
  ci:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest]
        php: ['8.1', '8.2', '8.3']

    name: PHP ${{ matrix.php }} - ${{ matrix.os }} - ${{ matrix.dependency-version }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php }}
        tools: composer:v2
        coverage: none

    - name: Install PHP dependencies
      run: composer update --prefer-stable --no-interaction --no-progress

    - name: Unit Tests
      run: composer test

    - name: Source Linter
      run: composer lint