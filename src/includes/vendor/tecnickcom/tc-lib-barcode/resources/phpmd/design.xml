<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="pcsg-generated-ruleset" 
    xmlns="http://pmd.sf.net/ruleset/1.0.0" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
    xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">
<description>Created with the PHP Coding Standard Generator. http://edorian.github.com/php-coding-standard-generator/
</description>
<rule ref="rulesets/design.xml/ExitExpression"/>
<rule ref="rulesets/design.xml/EvalExpression"/>
<rule ref="rulesets/design.xml/GotoStatement"/>
<rule ref="rulesets/design.xml/DepthOfInheritance">
    <properties>
        <property name="minimum" value="7"/>
    </properties>
</rule>
<rule ref="rulesets/design.xml/CouplingBetweenObjects"/>
</ruleset>
