<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON>cier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\PropertyInfo;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\PropertyInfo\Util\LegacyTypeConverter;
use Symfony\Component\TypeInfo\Type;

/**
 * Adds a PSR-6 cache layer on top of an extractor.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @final
 */
class PropertyInfoCacheExtractor implements PropertyInfoExtractorInterface, PropertyInitializableExtractorInterface
{
    private array $arrayCache = [];

    public function __construct(
        private readonly PropertyInfoExtractorInterface $propertyInfoExtractor,
        private readonly CacheItemPoolInterface $cacheItemPool,
    ) {
    }

    public function isReadable(string $class, string $property, array $context = []): ?bool
    {
        return $this->extract('isReadable', [$class, $property, $context]);
    }

    public function isWritable(string $class, string $property, array $context = []): ?bool
    {
        return $this->extract('isWritable', [$class, $property, $context]);
    }

    public function getShortDescription(string $class, string $property, array $context = []): ?string
    {
        return $this->extract('getShortDescription', [$class, $property, $context]);
    }

    public function getLongDescription(string $class, string $property, array $context = []): ?string
    {
        return $this->extract('getLongDescription', [$class, $property, $context]);
    }

    public function getProperties(string $class, array $context = []): ?array
    {
        return $this->extract('getProperties', [$class, $context]);
    }

    public function getType(string $class, string $property, array $context = []): ?Type
    {
        try {
            $serializedArguments = serialize([$class, $property, $context]);
        } catch (\Exception) {
            // If arguments are not serializable, skip the cache
            if (method_exists($this->propertyInfoExtractor, 'getType')) {
                return $this->propertyInfoExtractor->getType($class, $property, $context);
            }

            return LegacyTypeConverter::toTypeInfoType($this->propertyInfoExtractor->getTypes($class, $property, $context));
        }

        // Calling rawurlencode escapes special characters not allowed in PSR-6's keys
        $key = rawurlencode('getType.'.$serializedArguments);

        if (\array_key_exists($key, $this->arrayCache)) {
            return $this->arrayCache[$key];
        }

        $item = $this->cacheItemPool->getItem($key);

        if ($item->isHit()) {
            return $this->arrayCache[$key] = $item->get();
        }

        if (method_exists($this->propertyInfoExtractor, 'getType')) {
            $value = $this->propertyInfoExtractor->getType($class, $property, $context);
        } else {
            $value = LegacyTypeConverter::toTypeInfoType($this->propertyInfoExtractor->getTypes($class, $property, $context));
        }

        $item->set($value);
        $this->cacheItemPool->save($item);

        return $this->arrayCache[$key] = $value;
    }

    /**
     * @deprecated since Symfony 7.3, use "getType" instead
     */
    public function getTypes(string $class, string $property, array $context = []): ?array
    {
        trigger_deprecation('symfony/property-info', '7.3', 'The "%s()" method is deprecated, use "%s::getType()" instead.', __METHOD__, self::class);

        return $this->extract('getTypes', [$class, $property, $context]);
    }

    public function isInitializable(string $class, string $property, array $context = []): ?bool
    {
        return $this->extract('isInitializable', [$class, $property, $context]);
    }

    /**
     * Retrieves the cached data if applicable or delegates to the decorated extractor.
     */
    private function extract(string $method, array $arguments): mixed
    {
        try {
            $serializedArguments = serialize($arguments);
        } catch (\Exception) {
            // If arguments are not serializable, skip the cache
            return $this->propertyInfoExtractor->{$method}(...$arguments);
        }

        // Calling rawurlencode escapes special characters not allowed in PSR-6's keys
        $key = rawurlencode($method.'.'.$serializedArguments);

        if (\array_key_exists($key, $this->arrayCache)) {
            return $this->arrayCache[$key];
        }

        $item = $this->cacheItemPool->getItem($key);

        if ($item->isHit()) {
            return $this->arrayCache[$key] = $item->get();
        }

        $value = $this->propertyInfoExtractor->{$method}(...$arguments);
        $item->set($value);
        $this->cacheItemPool->save($item);

        return $this->arrayCache[$key] = $value;
    }
}
