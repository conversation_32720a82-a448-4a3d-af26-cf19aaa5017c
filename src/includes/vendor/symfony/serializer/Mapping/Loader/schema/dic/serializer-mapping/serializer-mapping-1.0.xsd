<?xml version="1.0" ?>

<xsd:schema xmlns="http://symfony.com/schema/dic/serializer-mapping"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://symfony.com/schema/dic/serializer-mapping"
            elementFormDefault="qualified">

    <xsd:annotation>
        <xsd:documentation><![CDATA[
      Symfony Serializer Mapping Schema, version 1.0
      Authors: <AUTHORS>

      A serializer mapping connects attributes with serialization groups.
    ]]></xsd:documentation>
    </xsd:annotation>

    <xsd:element name="serializer" type="serializer" />

    <xsd:complexType name="serializer">
        <xsd:annotation>
            <xsd:documentation><![CDATA[
        The root element of the serializer mapping definition.
      ]]></xsd:documentation>
        </xsd:annotation>
        <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="class" type="class" />
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="class">
        <xsd:annotation>
            <xsd:documentation><![CDATA[
        Contains serialization groups for a single class.

        Nested elements may be class property and/or getter definitions.
      ]]></xsd:documentation>
        </xsd:annotation>
        <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="attribute" type="attribute" minOccurs="0" maxOccurs="unbounded" />
            <xsd:element name="discriminator-map" type="discriminator-map" />
        </xsd:choice>
        <xsd:attribute name="name" type="xsd:string" use="required" />
    </xsd:complexType>

    <xsd:complexType name="discriminator-map">
        <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="mapping" type="discriminator-map-mapping" maxOccurs="unbounded" />
        </xsd:choice>
        <xsd:attribute name="type-property" type="xsd:string" use="required" />
        <xsd:attribute name="default-type" type="xsd:string" />
    </xsd:complexType>

    <xsd:complexType name="discriminator-map-mapping">
        <xsd:attribute name="type" type="xsd:string" use="required" />
        <xsd:attribute name="class" type="xsd:string" use="required" />
    </xsd:complexType>

    <xsd:complexType name="attribute">
        <xsd:annotation>
            <xsd:documentation><![CDATA[
        Contains serialization groups and max depth for attributes. The name of the attribute should be given in the "name" option.
      ]]></xsd:documentation>
        </xsd:annotation>
        <xsd:choice minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="group" type="xsd:string" maxOccurs="unbounded" />
            <xsd:element name="context" type="context" maxOccurs="unbounded" />
            <xsd:element name="normalization_context" type="context" maxOccurs="unbounded" />
            <xsd:element name="denormalization_context" type="context" maxOccurs="unbounded" />
        </xsd:choice>
        <xsd:attribute name="name" type="xsd:string" use="required" />
        <xsd:attribute name="max-depth">
            <xsd:simpleType>
                <xsd:restriction base="xsd:integer">
                    <xsd:minInclusive value="0" />
                </xsd:restriction>
            </xsd:simpleType>
        </xsd:attribute>
        <xsd:attribute name="serialized-name">
            <xsd:simpleType>
                <xsd:restriction base="xsd:string">
                    <xsd:minLength value="1" />
                </xsd:restriction>
            </xsd:simpleType>
        </xsd:attribute>
        <xsd:attribute name="serialized-path">
            <xsd:simpleType>
                <xsd:restriction base="xsd:string">
                    <xsd:minLength value="1" />
                </xsd:restriction>
            </xsd:simpleType>
        </xsd:attribute>
        <xsd:attribute name="ignore" type="xsd:boolean" />
    </xsd:complexType>

    <xsd:complexType name="context">
        <xsd:choice maxOccurs="unbounded">
            <xsd:element name="group" type="xsd:string" minOccurs="0" maxOccurs="unbounded" />
            <xsd:element name="entry" type="context-root-entry" maxOccurs="unbounded" />
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="context-root-entry" mixed="true">
        <xsd:sequence minOccurs="0">
            <xsd:element name="entry" type="context-entry" maxOccurs="unbounded" />
        </xsd:sequence>
        <xsd:attribute type="xsd:string" name="name" use="required" />
    </xsd:complexType>

    <xsd:complexType name="context-entry" mixed="true">
        <xsd:sequence minOccurs="0">
            <xsd:element name="entry" type="context-entry" maxOccurs="unbounded" />
        </xsd:sequence>
        <xsd:attribute type="xsd:string" name="name" />
    </xsd:complexType>

</xsd:schema>
