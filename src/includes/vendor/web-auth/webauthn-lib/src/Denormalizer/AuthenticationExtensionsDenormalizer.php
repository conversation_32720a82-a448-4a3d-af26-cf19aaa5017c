<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Denormalizer;

use <PERSON><PERSON><PERSON><PERSON>\Component\Serializer\Normalizer\DenormalizerInterface;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON><PERSON><PERSON>\AuthenticationExtensions\AuthenticationExtension;
use Webauthn\AuthenticationExtensions\AuthenticationExtensions;
use function assert;
use function is_array;
use function is_string;

final class AuthenticationExtensionsDenormalizer implements DenormalizerInterface, NormalizerInterface
{
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        if ($data instanceof AuthenticationExtensions) {
            return AuthenticationExtensions::create($data->extensions);
        }
        assert(is_array($data), 'The data should be an array.');
        foreach ($data as $key => $value) {
            if (! is_string($key)) {
                continue;
            }
            $data[$key] = AuthenticationExtension::create($key, $value);
        }

        return AuthenticationExtensions::create($data);
    }

    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = []
    ): bool {
        return $type === AuthenticationExtensions::class;
    }

    /**
     * @return array<class-string, bool>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            AuthenticationExtensions::class => true,
        ];
    }

    /**
     * @return array<string, mixed>
     */
    public function normalize(mixed $data, ?string $format = null, array $context = []): array
    {
        assert($data instanceof AuthenticationExtensions);
        $extensions = [];
        foreach ($data->extensions as $extension) {
            $extensions[$extension->name] = $extension->value;
        }

        return $extensions;
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof AuthenticationExtensions;
    }
}
