<?php

declare(strict_types=1);

namespace <PERSON><PERSON>hn\Denormalizer;

use <PERSON><PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\DenormalizerInterface;
use <PERSON><PERSON><PERSON>\CollectedClientData;
use const JSON_THROW_ON_ERROR;

final class CollectedClientDataDenormalizer implements DenormalizerInterface, DenormalizerAwareInterface
{
    use DenormalizerAwareTrait;

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        return CollectedClientData::create($data, json_decode($data, true, flags: JSON_THROW_ON_ERROR));
    }

    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = []
    ): bool {
        return $type === CollectedClientData::class;
    }

    /**
     * @return array<class-string, bool>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            CollectedClientData::class => true,
        ];
    }
}
