<?php

declare(strict_types=1);

namespace <PERSON><PERSON>hn\Denormalizer;

use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON><PERSON>hn\Exception\InvalidTrustPathException;
use <PERSON>authn\TrustPath\CertificateTrustPath;
use <PERSON><PERSON>hn\TrustPath\EmptyTrustPath;
use <PERSON><PERSON>hn\TrustPath\TrustPath;
use function array_key_exists;
use function assert;
use function is_array;

final class TrustPathDenormalizer implements DenormalizerInterface, NormalizerInterface
{
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        return match (true) {
            array_key_exists('x5c', $data) && is_array($data['x5c']) => CertificateTrustPath::create($data['x5c']),
            $data === [], isset($data['type']) && $data['type'] === EmptyTrustPath::class => EmptyTrustPath::create(),
            default => throw new InvalidTrustPathException('Unsupported trust path type'),
        };
    }

    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = []
    ): bool {
        return $type === TrustPath::class;
    }

    /**
     * @return array<class-string, bool>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            TrustPath::class => true,
        ];
    }

    /**
     * @return array<string, mixed>
     */
    public function normalize(mixed $data, ?string $format = null, array $context = []): array
    {
        assert($data instanceof TrustPath);
        return match (true) {
            $data instanceof CertificateTrustPath => [
                'x5c' => $data->certificates,
            ],
            $data instanceof EmptyTrustPath => [],
            default => throw new InvalidTrustPathException('Unsupported trust path type'),
        };
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof TrustPath;
    }
}
