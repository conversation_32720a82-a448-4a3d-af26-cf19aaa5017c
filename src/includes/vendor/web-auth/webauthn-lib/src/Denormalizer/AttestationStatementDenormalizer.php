<?php

declare(strict_types=1);

namespace <PERSON><PERSON>hn\Denormalizer;

use <PERSON>ymfony\Component\Serializer\Normalizer\DenormalizerInterface;
use <PERSON>authn\AttestationStatement\AttestationStatement;
use <PERSON><PERSON><PERSON>\AttestationStatement\AttestationStatementSupportManager;

final readonly class AttestationStatementDenormalizer implements DenormalizerInterface
{
    public function __construct(
        private AttestationStatementSupportManager $attestationStatementSupportManager
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $attestationStatementSupport = $this->attestationStatementSupportManager->get($data['fmt']);

        return $attestationStatementSupport->load($data);
    }

    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = []
    ): bool {
        return $type === AttestationStatement::class;
    }

    /**
     * @return array<class-string, bool>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            AttestationStatement::class => true,
        ];
    }
}
